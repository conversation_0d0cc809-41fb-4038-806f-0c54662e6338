import { COMPLIANCE_REQUIREMENTS, PAYRIX_LINKS } from "../constants/reviewConstants";
import { type ComplianceState, type ComplianceValidationErrors } from "../utils/reviewValidation";

interface ComplianceCheckboxesSectionProps {
  complianceState: ComplianceState;
  validationErrors: ComplianceValidationErrors;
  onChange: (field: keyof ComplianceState, value: boolean) => void;
}

interface ComplianceCheckboxProps {
  checked: boolean;
  onChange: (checked: boolean) => void;
  error?: string;
  children: React.ReactNode;
}

const ComplianceCheckbox = ({ checked, onChange, error, children }: ComplianceCheckboxProps) => (
  <div className={`bg-gray-50 border rounded-lg p-6 ${error ? "border-red-300 bg-red-50" : "border-gray-200"}`}>
    <label className="flex items-start cursor-pointer">
      <input
        type="checkbox"
        checked={checked}
        onChange={(e) => onChange(e.target.checked)}
        className="mt-1 w-4 h-4 text-blue-600 border-gray-300 rounded focus:ring-blue-500"
      />
      <span className="ml-3 text-sm text-gray-700">{children}</span>
    </label>
    {error && (
      <div className="mt-2 text-sm text-red-600 flex items-center">
        <svg className="w-4 h-4 mr-1 flex-shrink-0" fill="none" stroke="currentColor" viewBox="0 0 24 24">
          <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 8v4m0 4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z" />
        </svg>
        {error}
      </div>
    )}
  </div>
);

export const ComplianceCheckboxesSection = ({
  complianceState,
  validationErrors,
  onChange,
}: ComplianceCheckboxesSectionProps) => {
  const handleChange = (field: keyof ComplianceState, value: boolean) => {
    onChange(field, value);
  };

  return (
    <div className="mb-10">
      <h2 className="text-lg font-medium text-gray-900 mb-6">Terms & Conditions</h2>
      <div className="space-y-4">
        <ComplianceCheckbox
          checked={complianceState.tcAttestation}
          onChange={(value) => handleChange("tcAttestation", value)}
          error={validationErrors.tcAttestation}
        >
          {COMPLIANCE_REQUIREMENTS.TC_ATTESTATION.label}
        </ComplianceCheckbox>

        <ComplianceCheckbox
          checked={complianceState.visaDisclosure}
          onChange={(value) => handleChange("visaDisclosure", value)}
          error={validationErrors.visaDisclosure}
        >
          {COMPLIANCE_REQUIREMENTS.VISA_DISCLOSURE.label}
        </ComplianceCheckbox>

        <ComplianceCheckbox
          checked={complianceState.payrixTerms}
          onChange={(value) => handleChange("payrixTerms", value)}
          error={validationErrors.payrixTerms}
        >
          I accept the{" "}
          <a href={PAYRIX_LINKS.SUB_MERCHANT} target="_blank" rel="noopener noreferrer" className="text-blue-600 hover:underline">
            Payrix Sub-Merchant Agreement
          </a>
          ,{" "}
          <a href={PAYRIX_LINKS.DIRECT_MERCHANT} target="_blank" rel="noopener noreferrer" className="text-blue-600 hover:underline">
            Direct Merchant Agreement
          </a>
          ,{" "}
          <a href={PAYRIX_LINKS.AMEX} target="_blank" rel="noopener noreferrer" className="text-blue-600 hover:underline">
            AMEX Acceptance and Brand Requirements
          </a>
          , and applicable Bank Disclosures.
        </ComplianceCheckbox>

        <ComplianceCheckbox
          checked={complianceState.attestationStatement}
          onChange={(value) => handleChange("attestationStatement", value)}
          error={validationErrors.attestationStatement}
        >
          <strong>Attestation Statement:</strong> I attest that the information provided in this application is correct to the best of my
          knowledge as an authorized signer for this business. I have the authority to submit this application and bind the business to
          the agreements and terms presented.
        </ComplianceCheckbox>
      </div>
    </div>
  );
};
