import { COMPLIANCE_REQUIREMENTS } from "../constants/reviewConstants";

export interface ComplianceValidationErrors {
  tcAttestation?: string;
  visaDisclosure?: string;
  payrixTerms?: string;
  attestationStatement?: string;
}

export interface ComplianceState {
  tcAttestation: boolean;
  visaDisclosure: boolean;
  payrixTerms: boolean;
  attestationStatement: boolean;
}

export const validateComplianceCheckboxes = (state: ComplianceState): ComplianceValidationErrors => {
  const errors: ComplianceValidationErrors = {};

  if (!state.tcAttestation) {
    errors.tcAttestation = COMPLIANCE_REQUIREMENTS.TC_ATTESTATION.error;
  }

  if (!state.visaDisclosure) {
    errors.visaDisclosure = COMPLIANCE_REQUIREMENTS.VISA_DISCLOSURE.error;
  }

  if (!state.payrixTerms) {
    errors.payrixTerms = COMPLIANCE_REQUIREMENTS.PAYRIX_TERMS.error;
  }

  if (!state.attestationStatement) {
    errors.attestationStatement = COMPLIANCE_REQUIREMENTS.ATTESTATION_STATEMENT.error;
  }

  return errors;
};

export const hasValidationErrors = (errors: ComplianceValidationErrors): boolean => {
  return Object.keys(errors).length > 0;
};
